# <PERSON><PERSON> & <PERSON>dman Setup for Presentation AI

This document provides instructions for running the Presentation AI application using <PERSON><PERSON> or <PERSON><PERSON>.

## Prerequisites

### For <PERSON><PERSON> (Recommended)
- [<PERSON><PERSON>](https://podman.io/getting-started/installation) installed
- [podman-compose](https://github.com/containers/podman-compose) or docker-compose

### For Docker
- [Docker](https://docs.docker.com/get-docker/) installed
- [Docker Compose](https://docs.docker.com/compose/install/) installed

## Quick Start with Podman

### Option A: Full Stack (App + Database)

1. **Clone and setup environment**:
   ```bash
   # Copy environment file
   cp .env.example .env

   # Edit .env file with your values
   nano .env  # or your preferred editor
   ```

2. **Build and start services**:
   ```bash
   # Build the application
   ./scripts/podman-build.sh

   # Start all services
   ./scripts/podman-start.sh
   ```

3. **Access the application**:
   - Application: http://localhost:3000
   - Database: localhost:5432

4. **Stop services**:
   ```bash
   ./scripts/podman-stop.sh
   ```

### Option B: App Only (Using Local Database)

If you already have a local PostgreSQL database running:

1. **Setup environment**:
   ```bash
   # Use the helper script to configure .env properly
   ./scripts/setup-local-db-env.sh

   # Or manually copy and edit
   cp .env.example .env
   # Edit .env with your DATABASE_URL and OPENAI_API_KEY
   ```

2. **Important: Database URL for Containers**

   When running the app in a container but connecting to a local database, you cannot use `localhost` in the DATABASE_URL. Use one of these options:

   ```bash
   # Option 1: host.containers.internal (recommended)
   DATABASE_URL="postgresql://user:<EMAIL>:5432/presentation_ai"

   # Option 2: Your host machine's IP address
   DATABASE_URL="*********************************************************"

   # Option 3: localhost (only works with host networking)
   DATABASE_URL="*****************************************************"
   ```

3. **Run app with local database**:
   ```bash
   ./scripts/podman-local-db.sh
   ```

4. **Stop app**:
   ```bash
   podman-compose -f docker-compose.local-db.yml down
   ```

## Manual Commands

### Using Podman Compose

```bash
# Build
podman-compose -f docker-compose.podman.yml build

# Start services
podman-compose -f docker-compose.podman.yml up -d

# View logs
podman-compose -f docker-compose.podman.yml logs -f

# Stop services
podman-compose -f docker-compose.podman.yml down
```

### Using Docker Compose with Podman

```bash
# Set Podman socket (if needed)
export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"

# Build
docker-compose -f docker-compose.podman.yml build

# Start services
docker-compose -f docker-compose.podman.yml up -d

# Stop services
docker-compose -f docker-compose.podman.yml down
```

### Using Regular Docker

```bash
# Build
docker-compose build

# Start services
docker-compose up -d

# Stop services
docker-compose down
```

## Environment Variables

Required environment variables in `.env`:

```env
# Database (automatically set for Docker)
DATABASE_URL="**************************************/presentation_ai"

# OpenAI API Key (required)
OPENAI_API_KEY="your-openai-api-key-here"

# Node Environment
NODE_ENV="production"
```

## Troubleshooting

### Podman-specific Issues

1. **Permission issues with volumes**:
   ```bash
   # Run with user namespace mapping
   podman-compose -f docker-compose.podman.yml up -d --userns=keep-id
   ```

2. **Network connectivity issues**:
   - Uncomment `network_mode: host` in docker-compose.podman.yml
   - Or use `--network=host` flag

3. **Socket permission issues**:
   ```bash
   # Start Podman socket service
   systemctl --user start podman.socket
   systemctl --user enable podman.socket
   ```

### General Issues

1. **Port already in use**:
   ```bash
   # Check what's using the port
   lsof -i :3000
   lsof -i :5432
   
   # Change ports in docker-compose files if needed
   ```

2. **Build cache issues**:
   ```bash
   # Clear build cache
   podman system prune -a
   # or for Docker
   docker system prune -a
   ```

## Development

For development with hot reload, you can mount the source code:

```yaml
# Add to app service in docker-compose.yml
volumes:
  - .:/app
  - /app/node_modules
  - /app/.next
```

## Production Deployment

The Docker setup is optimized for production with:
- Multi-stage builds for smaller images
- Non-root user for security
- Standalone Next.js output
- Optimized layer caching

## File Structure

```
├── Dockerfile                 # Main Dockerfile
├── docker-compose.yml         # Standard Docker Compose
├── docker-compose.podman.yml  # Podman-optimized Compose
├── .dockerignore             # Docker ignore file
└── scripts/
    ├── podman-build.sh       # Build script for Podman
    ├── podman-start.sh       # Start script for Podman
    └── podman-stop.sh        # Stop script for Podman
```
