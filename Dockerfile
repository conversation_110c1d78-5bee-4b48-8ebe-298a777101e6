# Use the official Node.js 18 image as base
FROM node:18-alpine AS base

# Install OpenSSL for Prisma
RUN apk add --no-cache openssl && corepack enable && corepack prepare pnpm@10.16.0 --activate

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Enable corepack and install dependencies using pnpm
## corepack already enabled in base
COPY package.json pnpm-lock.yaml* ./
RUN \
  if [ -f pnpm-lock.yaml ]; then pnpm install --prod --no-frozen-lockfile; \
  else echo "pnpm-lock.yaml not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Copy package files and prisma schema first (pnpm)
COPY package.json pnpm-lock.yaml* ./
COPY prisma ./prisma
COPY .npmrc ./

# Set Prisma to use the correct binary platform
ENV PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x"

# Install all dependencies (including dev dependencies) for building
RUN \
  if [ -f pnpm-lock.yaml ]; then pnpm install --no-frozen-lockfile; \
  else echo "pnpm-lock.yaml not found." && exit 1; \
  fi

# Copy the rest of the source code
COPY . .

# Ensure public directory exists
RUN mkdir -p public

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED 1

# Skip environment validation during build
ENV SKIP_ENV_VALIDATION 1

# Build the application
RUN pnpm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

# Install OpenSSL for Prisma in runtime
RUN apk add --no-cache openssl

ENV NODE_ENV production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED 1

# Create a non-root user for security
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the public folder
COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]
