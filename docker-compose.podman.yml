version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/presentation_ai
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - presentation-network
    # Podman-specific: Use host networking for better compatibility
    # network_mode: host  # Uncomment if needed for Podman networking issues

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=presentation_ai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - presentation-network
    # Podman-specific: Ensure proper volume permissions
    user: "999:999"  # postgres user in the container

volumes:
  postgres_data:
    # Podman-specific: Use local driver explicitly
    driver: local

networks:
  presentation-network:
    driver: bridge
