#!/bin/bash

# Script to help check database connection from container

set -e

echo "🔍 Checking database connection options..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    if [ "$1" = "success" ]; then
        echo -e "${GREEN}✅ $2${NC}"
    elif [ "$1" = "warning" ]; then
        echo -e "${YELLOW}⚠️  $2${NC}"
    elif [ "$1" = "info" ]; then
        echo -e "${BLUE}ℹ️  $2${NC}"
    else
        echo "$2"
    fi
}

print_status "info" "Current DATABASE_URL in .env:"
grep "^DATABASE_URL=" .env 2>/dev/null || echo "DATABASE_URL not found in .env"

echo ""
print_status "info" "Your host machine's IP addresses:"
if command -v hostname &> /dev/null; then
    hostname -I 2>/dev/null || ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -3
fi

echo ""
print_status "info" "Testing if PostgreSQL is running locally:"
if lsof -Pi :5432 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_status "success" "PostgreSQL is running on port 5432"
    
    # Try to get more info about the PostgreSQL process
    echo ""
    print_status "info" "PostgreSQL process details:"
    lsof -Pi :5432 -sTCP:LISTEN 2>/dev/null | head -5
else
    print_status "warning" "No process found listening on port 5432"
fi

echo ""
print_status "info" "For Podman with local database, your DATABASE_URL should be:"
echo "DATABASE_URL=\"postgresql://your_username:<EMAIL>:5432/your_database\""
echo ""
print_status "info" "Replace 'your_username', 'your_password', and 'your_database' with your actual values."

echo ""
print_status "info" "To test the connection from within a container, you can run:"
echo "podman run --rm -it --add-host=host.containers.internal:host-gateway postgres:15-alpine psql 'postgresql://user:<EMAIL>:5432/dbname'"
