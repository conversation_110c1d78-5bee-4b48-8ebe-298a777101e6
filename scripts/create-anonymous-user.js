#!/usr/bin/env node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createAnonymousUser() {
  try {
    console.log('Creating anonymous user...');
    
    // Check if anonymous user already exists
    const existingUser = await prisma.user.findUnique({
      where: { id: 'anonymous-user' }
    });

    if (existingUser) {
      console.log('Anonymous user already exists');
      return;
    }

    // Create the anonymous user
    const anonymousUser = await prisma.user.create({
      data: {
        id: 'anonymous-user',
        name: 'Anonymous User',
        email: '<EMAIL>',
        role: 'USER',
        hasAccess: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    });

    console.log('Anonymous user created successfully:', anonymousUser.id);
  } catch (error) {
    console.error('Error creating anonymous user:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createAnonymousUser();
