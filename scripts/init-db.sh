#!/bin/bash

# Database initialization script for presentation-ai

set -e

echo "🗄️  Initializing database..."

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    COMPOSE_CMD="podman-compose"
    COMPOSE_FILE="docker-compose.podman.yml"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
    COMPOSE_FILE="docker-compose.yml"
else
    echo "❌ Neither podman-compose nor docker-compose is available."
    exit 1
fi

# Set environment variables for Podman
if [[ "$COMPOSE_CMD" == "podman-compose" ]]; then
    export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"
fi

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
$COMPOSE_CMD -f $COMPOSE_FILE exec db pg_isready -U postgres -d presentation_ai || {
    echo "⚠️  Database not ready. Make sure services are running:"
    echo "  ./scripts/podman-start.sh"
    exit 1
}

# Run Prisma migrations
echo "🔄 Running Prisma database push..."
$COMPOSE_CMD -f $COMPOSE_FILE exec app npx prisma db push

echo "✅ Database initialized successfully!"
echo ""
echo "🌐 You can now access the application at: http://localhost:3000"
