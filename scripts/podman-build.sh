#!/bin/bash

# <PERSON><PERSON> build script for presentation-ai

set -e

echo "🐳 Building presentation-ai with <PERSON><PERSON>..."

# Check if <PERSON><PERSON> is installed
if ! command -v podman &> /dev/null; then
    echo "❌ <PERSON>dman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    echo "✅ Using podman-compose"
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    echo "✅ Using docker-compose with <PERSON><PERSON>"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither podman-compose nor docker-compose is available."
    echo "Please install podman-compose or docker-compose."
    exit 1
fi

# Set environment variables
export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"

# Build the application
echo "🔨 Building the application..."
$COMPOSE_CMD -f docker-compose.podman.yml build --no-cache

echo "✅ Build completed successfully!"
echo ""
echo "To start the application, run:"
echo "  ./scripts/podman-start.sh"
