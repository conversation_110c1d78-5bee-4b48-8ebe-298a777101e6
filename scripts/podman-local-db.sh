#!/bin/bash

# <PERSON><PERSON> script for running with local database

set -e

echo "🚀 Starting presentation-ai with <PERSON><PERSON> (using local database)..."

# Check if <PERSON><PERSON> is installed
if ! command -v podman &> /dev/null; then
    echo "❌ <PERSON>dman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    echo "✅ Using podman-compose"
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    echo "✅ Using docker-compose with <PERSON><PERSON>"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither podman-compose nor docker-compose is available."
    echo "Please install podman-compose or docker-compose."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your actual values before continuing."
        echo "   Required: OPENAI_API_KEY, DATABASE_URL (pointing to your local database)"
        exit 1
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Check if DATABASE_URL is set appropriately for containerized app
if grep -q "localhost\|127.0.0.1" .env; then
    echo "⚠️  DATABASE_URL in .env uses localhost, but the app will run in a container."
    echo "   For Podman with local database, use:"
    echo "   DATABASE_URL=\"postgresql://username:<EMAIL>:5432/presentation_ai\""
    echo "   Or the script will use host networking to connect to localhost."
fi

# Set environment variables
export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"

# Build if needed
echo "🔨 Building application..."
$COMPOSE_CMD -f docker-compose.local-db.yml build

# Start the service
echo "🔄 Starting application..."
$COMPOSE_CMD -f docker-compose.local-db.yml up -d

echo "✅ Application started successfully!"
echo ""
echo "🌐 Application is running at: http://localhost:3000"
echo "🗄️  Using your local database"
echo ""
echo "To view logs, run:"
echo "  $COMPOSE_CMD -f docker-compose.local-db.yml logs -f"
echo ""
echo "To stop the application, run:"
echo "  $COMPOSE_CMD -f docker-compose.local-db.yml down"
