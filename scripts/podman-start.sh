#!/bin/bash

# <PERSON><PERSON> start script for presentation-ai

set -e

echo "🚀 Starting presentation-ai with <PERSON><PERSON>..."

# Check if <PERSON><PERSON> is installed
if ! command -v podman &> /dev/null; then
    echo "❌ <PERSON>dman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    echo "✅ Using podman-compose"
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    echo "✅ Using docker-compose with <PERSON><PERSON>"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither podman-compose nor docker-compose is available."
    echo "Please install podman-compose or docker-compose."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your actual values before continuing."
        echo "   Required: OPENAI_API_KEY"
        exit 1
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Set environment variables
export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"

# Start the services
echo "🔄 Starting services..."
$COMPOSE_CMD -f docker-compose.podman.yml up -d

echo "✅ Services started successfully!"
echo ""
echo "🌐 Application is running at: http://localhost:3000"
echo "🗄️  Database is running at: localhost:5432"
echo ""
echo "To view logs, run:"
echo "  $COMPOSE_CMD -f docker-compose.podman.yml logs -f"
echo ""
echo "To stop the services, run:"
echo "  ./scripts/podman-stop.sh"
