#!/bin/bash

# <PERSON><PERSON> stop script for presentation-ai

set -e

echo "🛑 Stopping presentation-ai services..."

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    echo "✅ Using podman-compose"
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    echo "✅ Using docker-compose with <PERSON><PERSON>"
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither podman-compose nor docker-compose is available."
    echo "Please install podman-compose or docker-compose."
    exit 1
fi

# Set environment variables
export DOCKER_HOST="unix:///run/user/$(id -u)/podman/podman.sock"

# Stop the services
$COMPOSE_CMD -f docker-compose.podman.yml down

echo "✅ Services stopped successfully!"
echo ""
echo "To remove volumes (⚠️  this will delete all data), run:"
echo "  $COMPOSE_CMD -f docker-compose.podman.yml down -v"
