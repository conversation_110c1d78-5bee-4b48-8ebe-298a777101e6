#!/bin/bash

# Script to help setup .env for Podman with local database

set -e

echo "🔧 Setting up .env for Podman with local database..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    if [ "$1" = "success" ]; then
        echo -e "${GREEN}✅ $2${NC}"
    elif [ "$1" = "error" ]; then
        echo -e "${RED}❌ $2${NC}"
    elif [ "$1" = "warning" ]; then
        echo -e "${YELLOW}⚠️  $2${NC}"
    elif [ "$1" = "info" ]; then
        echo -e "${BLUE}ℹ️  $2${NC}"
    else
        echo "$2"
    fi
}

# Check if .env exists
if [ ! -f .env ]; then
    print_status "info" "Creating .env from .env.example..."
    cp .env.example .env
fi

print_status "info" "Current DATABASE_URL in .env:"
grep "^DATABASE_URL=" .env || echo "DATABASE_URL not found"

echo ""
print_status "info" "For Podman with local database, you have several options:"
echo ""
echo "1. Use host.containers.internal (works with recent Podman versions):"
echo "   DATABASE_URL=\"postgresql://username:<EMAIL>:5432/presentation_ai\""
echo ""
echo "2. Use host networking (app runs on host network):"
echo "   DATABASE_URL=\"**************************************************************""
echo "   (This is handled automatically by docker-compose.local-db.yml)"
echo ""
echo "3. Use host IP address directly:"
echo "   DATABASE_URL=\"***************************************** -I | awk '{print $1}'):5432/presentation_ai\""
echo ""

read -p "Would you like to update the DATABASE_URL? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "Please enter your database connection details:"
    read -p "Database username: " db_user
    read -s -p "Database password: " db_pass
    echo
    read -p "Database name [presentation_ai]: " db_name
    db_name=${db_name:-presentation_ai}
    read -p "Database port [5432]: " db_port
    db_port=${db_port:-5432}
    
    echo ""
    echo "Choose connection method:"
    echo "1. host.containers.internal (recommended)"
    echo "2. localhost (with host networking)"
    echo "3. host IP address"
    read -p "Enter choice (1-3): " choice
    
    case $choice in
        1)
            db_host="host.containers.internal"
            ;;
        2)
            db_host="localhost"
            ;;
        3)
            db_host=$(hostname -I | awk '{print $1}')
            print_status "info" "Using host IP: $db_host"
            ;;
        *)
            print_status "error" "Invalid choice"
            exit 1
            ;;
    esac
    
    new_db_url="postgresql://${db_user}:${db_pass}@${db_host}:${db_port}/${db_name}"
    
    # Update .env file
    if grep -q "^DATABASE_URL=" .env; then
        sed -i.bak "s|^DATABASE_URL=.*|DATABASE_URL=\"${new_db_url}\"|" .env
    else
        echo "DATABASE_URL=\"${new_db_url}\"" >> .env
    fi
    
    print_status "success" "Updated DATABASE_URL in .env"
fi

echo ""
print_status "info" "Don't forget to set your OPENAI_API_KEY in .env"
print_status "info" "Current .env file:"
echo "----------------------------------------"
cat .env
echo "----------------------------------------"
