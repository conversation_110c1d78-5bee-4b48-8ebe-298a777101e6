#!/bin/bash

# Test script for <PERSON><PERSON> setup

set -e

echo "🧪 Testing Podman setup for presentation-ai..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ "$1" = "success" ]; then
        echo -e "${GREEN}✅ $2${NC}"
    elif [ "$1" = "error" ]; then
        echo -e "${RED}❌ $2${NC}"
    elif [ "$1" = "warning" ]; then
        echo -e "${YELLOW}⚠️  $2${NC}"
    else
        echo "ℹ️  $2"
    fi
}

# Check if <PERSON><PERSON> is installed
if command -v podman &> /dev/null; then
    print_status "success" "Podman is installed"
    podman --version
else
    print_status "error" "Podman is not installed"
    exit 1
fi

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    print_status "success" "podman-compose is available"
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    print_status "warning" "Using docker-compose with <PERSON><PERSON>"
    COMPOSE_CMD="docker-compose"
else
    print_status "error" "Neither podman-compose nor docker-compose is available"
    exit 1
fi

# Check if .env file exists
if [ -f .env ]; then
    print_status "success" ".env file exists"
    
    # Check if OPENAI_API_KEY is set
    if grep -q "OPENAI_API_KEY=" .env && ! grep -q "OPENAI_API_KEY=\"your-openai-api-key-here\"" .env; then
        print_status "success" "OPENAI_API_KEY is configured"
    else
        print_status "warning" "OPENAI_API_KEY needs to be configured in .env"
    fi
else
    print_status "warning" ".env file not found - will be created from .env.example"
fi

# Test Podman socket
if [ -S "/run/user/$(id -u)/podman/podman.sock" ]; then
    print_status "success" "Podman socket is available"
else
    print_status "warning" "Podman socket not found - you may need to start it:"
    echo "  systemctl --user start podman.socket"
fi

# Check if ports are available
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_status "warning" "Port 3000 is already in use"
else
    print_status "success" "Port 3000 is available"
fi

if lsof -Pi :5432 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_status "warning" "Port 5432 is already in use"
else
    print_status "success" "Port 5432 is available"
fi

# Test Docker/Podman compose file syntax
print_status "info" "Testing compose file syntax..."
if $COMPOSE_CMD -f docker-compose.podman.yml config >/dev/null 2>&1; then
    print_status "success" "docker-compose.podman.yml syntax is valid"
else
    print_status "error" "docker-compose.podman.yml has syntax errors"
    exit 1
fi

print_status "success" "All tests passed! You can now run:"
echo "  ./scripts/podman-build.sh"
echo "  ./scripts/podman-start.sh"
