"use server";
import OpenAI from "openai";
import { env } from "@/env";

export async function testOpenAIConnectivity() {
  try {
    const client = new OpenAI({ apiKey: env.OPENAI_API_KEY });
    // A very lightweight call: list models (could be restricted by some keys, handle errors)
    const models = await client.models.list();
    return {
      ok: true,
      modelCount: models.data.length,
    };
  } catch (err) {
    const message = err instanceof Error ? err.message : "Unknown error";
    return { ok: false, error: message };
  }
}
