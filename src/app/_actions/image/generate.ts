"use server";

import { env } from "@/env";
import OpenAI from "openai";
import { db } from "@/server/db";
import { utapi } from "@/app/api/uploadthing/core";
import { UTFile } from "uploadthing/server";

function createOpenAI() {
  return new OpenAI({ apiKey: env.OPENAI_API_KEY });
}

export type ImageModelList =
  | "dall-e-2"
  | "dall-e-3";

// Default anonymous user ID for open access
const ANONYMOUS_USER_ID = "anonymous-user";

// Enhanced prompt function for better quality images
function enhancePrompt(originalPrompt: string): string {
  // Quality enhancement keywords
  const qualityEnhancements = [
    "high quality",
    "professional",
    "detailed",
    "crisp",
    "clear",
    "well-lit",
    "sharp focus"
  ];

  // Check if prompt mentions people/humans and add specific enhancements
  const peopleKeywords = ["person", "people", "human", "man", "woman", "child", "face", "portrait"];
  const hasPeople = peopleKeywords.some(keyword =>
    originalPrompt.toLowerCase().includes(keyword)
  );

  let enhancedPrompt = originalPrompt;

  // Add quality enhancements if not already present
  const hasQualityKeywords = qualityEnhancements.some(keyword =>
    originalPrompt.toLowerCase().includes(keyword)
  );

  if (!hasQualityKeywords) {
    enhancedPrompt += ", high quality, professional, detailed";
  }

  // Add specific enhancements for people to avoid distortion
  if (hasPeople) {
    enhancedPrompt += ", realistic proportions, natural anatomy, clear facial features, professional photography style";
  }

  // Add general photographic quality
  enhancedPrompt += ", sharp focus, good lighting, photorealistic";

  return enhancedPrompt;
}

export async function generateImageAction(
  prompt: string,
  model: ImageModelList = "dall-e-3",
  options?: { force?: boolean }
) {
  const userId = ANONYMOUS_USER_ID;
  try {
    // Enhance first so cache key matches stored value
    const enhancedPrompt = enhancePrompt(prompt);
    if (!options?.force) {
      // Attempt to reuse existing generated image for identical enhanced prompt
      const existing = await db.generatedImage.findFirst({
        where: { prompt: enhancedPrompt, userId },
        orderBy: { createdAt: 'desc' },
      });
      if (existing) {
        return { success: true, image: existing, reused: true as const };
      }
    }
    console.log(`Generating image (model=${model}) force=${!!options?.force}`);
    console.log(`Enhanced prompt: ${enhancedPrompt}`);
    const response = await createOpenAI().images.generate({
      model,
      prompt: enhancedPrompt,
      size: model === 'dall-e-3' ? '1024x1024' : '1024x1024',
      quality: model === 'dall-e-3' ? 'hd' : undefined,
      style: model === 'dall-e-3' ? 'natural' : undefined,
      n: 1,
    });
    const imageUrl = response.data?.[0]?.url;
    if (!imageUrl) throw new Error('Failed to generate image');
    // Persist / upload
    let permanentUrl: string;
    if (env.UPLOADTHING_TOKEN) {
      try {
        const imageResponse = await fetch(imageUrl);
        if (!imageResponse.ok) throw new Error('Failed to download image from OpenAI');
        const imageBlob = await imageResponse.blob();
        const imageBuffer = await imageBlob.arrayBuffer();
        const filename = `${prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_')}_${Date.now()}.png`;
        const utFile = new UTFile([new Uint8Array(imageBuffer)], filename);
        const uploadResult = await utapi.uploadFiles([utFile]);
        if (!uploadResult[0]?.data?.url) {
          console.error('Upload error:', uploadResult[0]?.error);
          throw new Error('Failed to upload image to UploadThing');
        }
        permanentUrl = uploadResult[0].data.url;
      } catch (uploadError) {
        console.error('UploadThing upload failed, using OpenAI URL:', uploadError);
        permanentUrl = imageUrl;
      }
    } else {
      if (process.env.NODE_ENV !== 'production' && !process.env.__UT_FALLBACK_NOTIFIED) {
        console.info('UploadThing token not set – storing OpenAI URL directly. Set UPLOADTHING_TOKEN to enable permanent storage.');
        process.env.__UT_FALLBACK_NOTIFIED = '1';
      }
      permanentUrl = imageUrl;
    }
    const generatedImage = await db.generatedImage.create({
      data: { url: permanentUrl, prompt: enhancedPrompt, userId },
    });
    return { success: true, image: generatedImage, reused: false as const };
  } catch (error) {
    console.error('Error generating image:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Failed to generate image' };
  }
}
