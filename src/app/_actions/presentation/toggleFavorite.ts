"use server";

import { db } from "@/server/db";

// Default anonymous user ID for open access
const ANONYMOUS_USER_ID = "anonymous-user";

export async function addToFavorites(documentId: string) {
  // Use anonymous user for open access
  const userId = ANONYMOUS_USER_ID;

  try {
    // Check if already favorited
    const existing = await db.favoriteDocument.findFirst({
      where: {
        documentId,
        userId,
      },
    });

    if (existing) {
      return { error: "Document is already in favorites" };
    }

    // Add to favorites
    await db.favoriteDocument.create({
      data: {
        documentId,
        userId,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error adding to favorites:", error);
    return { error: "Failed to add to favorites" };
  }
}

export async function removeFromFavorites(documentId: string) {
  // Use anonymous user for open access
  const userId = ANONYMOUS_USER_ID;

  try {
    await db.favoriteDocument.deleteMany({
      where: {
        documentId,
        userId,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error removing from favorites:", error);
    return { error: "Failed to remove from favorites" };
  }
}
