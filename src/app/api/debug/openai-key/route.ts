import { NextResponse } from "next/server";
import { env } from "@/env";

// Simple internal diagnostic endpoint (remove or protect in production!)
export async function GET() {
  const key = env.OPENAI_API_KEY;
  if (!key) {
    return NextResponse.json({ present: false }, { status: 200 });
  }
  return NextResponse.json({
    present: true,
    length: key.length,
    startsWithSk: key.startsWith("sk-"),
    hasWhitespace: /\s/.test(key),
    hasBearerPrefix: key.startsWith("Bearer "),
  });
}
