import { NextResponse } from 'next/server';

// Simple image proxy to bypass CORS issues for export rendering.
// Usage: /api/image-proxy?url=<encoded>
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const target = searchParams.get('url');
  if (!target) {
    return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
  }
  try {
    const res = await fetch(target, { cache: 'no-store' });
    if (!res.ok || !res.body) {
      return NextResponse.json({ error: 'Failed to fetch image' }, { status: 502 });
    }
  const contentType = res.headers.get('content-type') ?? 'image/png';
    const arrayBuffer = await res.arrayBuffer();
    return new NextResponse(arrayBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'no-store',
      },
    });
  } catch (e) {
    return NextResponse.json({ error: 'Proxy error', message: (e as Error).message }, { status: 500 });
  }
}
