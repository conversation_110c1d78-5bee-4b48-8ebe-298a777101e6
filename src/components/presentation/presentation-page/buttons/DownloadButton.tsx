"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, FileText, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { usePresentationState } from "@/states/presentation-state";
import { exportToPDF, exportToPPTX } from "@/lib/presentation/export";

export function DownloadButton() {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();
  const { slides, currentPresentationTitle } = usePresentationState();

  const handleExport = async (format: "pdf" | "pptx") => {
    if (!slides || slides.length === 0) {
      toast({
        title: "No content to export",
        description: "Please create some slides first.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      const filename = currentPresentationTitle ?? "presentation";
      
      switch (format) {
        case "pdf":
          await exportToPDF(slides, filename);
          toast({
            title: "PDF exported successfully",
            description: "Your presentation has been downloaded as a PDF.",
          });
          break;
        case "pptx":
          try {
            await exportToPPTX(slides, filename);
            toast({
              title: "PowerPoint exported successfully",
              description: "Your presentation has been downloaded as a PPTX file.",
            });
          } catch (error) {
            toast({
              title: "PPTX export not available",
              description: "PowerPoint export is coming soon. Please use PDF or PNG export instead.",
              variant: "destructive",
            });
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export failed",
        description: "There was an error exporting your presentation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-foreground"
          disabled={isExporting}
        >
          {isExporting ? (
            <Loader2 className="mr-1 h-4 w-4 animate-spin" />
          ) : (
            <Download className="mr-1 h-4 w-4" />
          )}
          Download
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => handleExport("pdf")}
          disabled={isExporting}
        >
          <FileText className="mr-2 h-4 w-4" />
          Export as PDF
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleExport("pptx")}
          disabled={isExporting}
        >
          <FileText className="mr-2 h-4 w-4" />
          Export as PowerPoint
        </DropdownMenuItem>
        {/* Removed PNG and JSON export options per user request */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
