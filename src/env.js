import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

// Aggressive sanitizer: trims ends and removes internal whitespace/newline characters.
function sanitizeKey(raw) {
  if (!raw || typeof raw !== 'string') return undefined;
  const trimmed = raw.trim();
  const collapsed = trimmed.replace(/\s+/g, "");
  if (collapsed !== raw) {
    // One-time console notice so operators know key had formatting issues.
    if (process.env.NODE_ENV !== "production") {
      console.warn("OPENAI_API_KEY contained whitespace/newlines that were removed.");
    }
  }
  return collapsed;
}

export const env = createEnv({
  server: {
    DATABASE_URL: z.string().url(),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),

    OPENAI_API_KEY: z.string(),
    UPLOADTHING_TOKEN: z.string().optional(),
  },

  runtimeEnv: {
    DATABASE_URL: process.env.DATABASE_URL,
    NODE_ENV: process.env.NODE_ENV,
  OPENAI_API_KEY: sanitizeKey(process.env.OPENAI_API_KEY),
    UPLOADTHING_TOKEN: process.env.UPLOADTHING_TOKEN,
  },

  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  emptyStringAsUndefined: true,
});

// Ensure any downstream libraries that read directly from process.env get the sanitized value.
if (env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== env.OPENAI_API_KEY) {
  process.env.OPENAI_API_KEY = env.OPENAI_API_KEY;
}

// Defensive check: warn (without leaking secret) if key seems malformed (e.g., user pasted full header or added Bearer prefix)
if (env.OPENAI_API_KEY) {
  if (env.OPENAI_API_KEY.startsWith("Bearer")) {
    console.warn("OPENAI_API_KEY should not include the 'Bearer ' prefix. Remove it from the environment value.");
  }
  if (!env.OPENAI_API_KEY.startsWith("sk-")) {
    console.warn("OPENAI_API_KEY does not start with expected 'sk-' prefix – verify the key is correct.");
  }
}
