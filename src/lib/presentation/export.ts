import { type PlateSlide, type PlateNode, type HeadingElement, type ParagraphElement } from "@/components/presentation/utils/parser";
// pptxgenjs types (no official TS types with ESM default in some versions). Create minimal interface.
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
// PPTX export temporarily disabled (pptxgenjs not installed in container build). Placeholder kept.
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// (downloadFile helper removed – no longer used)

// Helper function to wait for images to load
function waitForImages(element: HTMLElement): Promise<void> {
  const images = element.querySelectorAll("img");
  const promises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();
    return new Promise<void>((resolve) => {
      img.onload = () => resolve();
      img.onerror = () => resolve(); // Continue even if image fails to load
      // Timeout after 10 seconds
      setTimeout(() => resolve(), 10000);
    });
  });
  return Promise.all(promises).then(() => undefined);
}

// Inline external <img> elements by converting them to data URLs to stabilize exports (PDF/PPTX)
async function inlineImages(container: HTMLElement, tag: string) {
  const imgs = Array.from(container.querySelectorAll('img')).filter((el): el is HTMLImageElement => el instanceof HTMLImageElement);
  await Promise.all(imgs.map(async (img) => {
    if (!img.src || img.src.startsWith('data:')) return;
    try {
      const bust = img.src.includes('?') ? '&' : '?';
      const fetchUrl = img.src + bust + encodeURIComponent(tag) + '=1';
      let res = await fetch(fetchUrl, { mode: 'cors' });
      if (!res.ok) {
        const proxied = '/api/image-proxy?url=' + encodeURIComponent(img.src);
        res = await fetch(proxied);
        if (!res.ok) throw new Error('Proxy fetch failed: ' + res.status);
      }
      const blob = await res.blob();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(blob);
      });
      img.setAttribute('data-original-src', img.src);
      img.removeAttribute('srcset');
      img.loading = 'eager';
      img.src = dataUrl;
    } catch (err) {
      img.crossOrigin = 'anonymous';
    }
  }));
}



// Export slides as PDF
export async function exportToPDF(slides: PlateSlide[], filename: string) {
  const pdf = new jsPDF({ orientation: "landscape", unit: "px", format: [1920, 1080] });

  // Helper: convert external images to data URLs to avoid CORS / authentication issues.
  // (Removed inlineImages local function; using shared inlineImages())

  for (let i = 0; i < slides.length; i++) {
    const slideElement = document.querySelector(`.slide-wrapper-${i} .slide-container-${i}`);
    if (!slideElement) { console.warn(`Slide ${i} element not found, skipping...`); continue; }

    // Clone node to avoid mutating on-screen state while inlining
    const working = slideElement.cloneNode(true) as HTMLElement; // cloneNode returns Node
    // Fix width/height to ensure consistent rendering
    working.style.width = '1920px';
    working.style.height = '1080px';
    document.body.appendChild(working);
  await inlineImages(working, 'pdf');
    await waitForImages(working);

    // One more short delay for rendering fonts/images
    await new Promise(r => setTimeout(r, 300));

    // html2canvas on the cloned node (off-DOM)
  // already appended
    const canvas = await html2canvas(working, {
      scale: 2,
      useCORS: true,
      allowTaint: true, // allowTaint since we now mostly have data URLs
      backgroundColor: '#ffffff',
      width: 1920,
      height: 1080,
      logging: false,
      imageTimeout: 15000,
    });
  document.body.removeChild(working);

    const imgData = canvas.toDataURL('image/png');
    if (i > 0) pdf.addPage();
    pdf.addImage(imgData, 'PNG', 0, 0, 1920, 1080);
  }

  pdf.save(`${filename}.pdf`);
}

// Export slides as PNG images
// PNG export removed

// Disabled loader
// Removed PPTX text extraction while feature disabled.

// Export slides as PowerPoint (PPTX)
interface GenericNode { text?: string; children?: GenericNode[]; type?: string }

function isHeading(n: PlateNode): n is HeadingElement {
  return ["h1","h2","h3","h4","h5","h6"].includes(n.type as string);
}
function isParagraph(n: PlateNode): n is ParagraphElement { return n.type === "p"; }

function flattenText(nodes?: GenericNode[]): string {
  if (!nodes || nodes.length === 0) return "";
  const out: string[] = [];
  const stack: GenericNode[] = [...nodes];
  while (stack.length) {
    const n = stack.shift()!;
    if (typeof n.text === "string" && n.text.trim().length) out.push(n.text);
  if (n.children?.length) stack.unshift(...n.children);
  }
  return out.join(" ").replace(/\s+/g, " ").trim();
}

function extractSlideStructure(slide: PlateSlide) {
  let title: string | undefined;
  const bullets: string[] = [];
  const paragraphs: string[] = [];
  for (const node of slide.content) {
    const g = node as unknown as GenericNode;
    if (!title && isHeading(node)) { title = flattenText(g.children); continue; }
    if (g.type === "bullets" && g.children?.length) {
      for (const child of g.children) {
        const t = flattenText(child.children);
        if (t) bullets.push(t);
      }
      continue;
    }
    if (isParagraph(node)) {
      const p = flattenText(g.children);
      if (p) paragraphs.push(p);
      continue;
    }
  }
  return { title: title ?? "Slide", bullets, paragraphs };
}

async function fallbackRasterPptx(slides: PlateSlide[], filename: string) {
  const { default: PptxGen } = await import('pptxgenjs');
  const pptx = new PptxGen(); pptx.layout='LAYOUT_16x9';
  for (const [i] of slides.entries()) {
    const el = document.querySelector(`.slide-wrapper-${i} .slide-container-${i}`);
    if (!el) { continue; }
    const working = el.cloneNode(true) as HTMLElement;
    working.style.width='1920px'; working.style.height='1080px';
    document.body.appendChild(working);
  await inlineImages(working, 'pptx');
  await waitForImages(working);
    await new Promise(r=>setTimeout(r,150));
    const canvas = await html2canvas(working,{scale:2,useCORS:true,allowTaint:true,backgroundColor:'#ffffff',width:1920,height:1080,logging:false,imageTimeout:15000});
    document.body.removeChild(working);
    const img = canvas.toDataURL('image/png');
    const slide = pptx.addSlide();
    slide.addImage({ data: img, x:0,y:0,w:pptx.width,h:pptx.height });
  }
  await pptx.writeFile({ fileName: `${filename}.pptx` });
}

export async function exportToPPTX(slides: PlateSlide[], filename: string) {
  if (typeof window === 'undefined') return;
  try {
    const { default: PptxGen } = await import('pptxgenjs');
    const pptx = new PptxGen(); pptx.layout='LAYOUT_16x9';
    for (const s of slides) {
      if (!s) continue;
      const { title, bullets, paragraphs } = extractSlideStructure(s);
      const slide = pptx.addSlide();
      const margin = 0.4;
      let x = margin; let w = pptx.width - margin*2; let y = margin;
  const layout = s.layoutType;
  const imgUrl = s.rootImage?.url;
      if (imgUrl) {
        try {
          const proxied = '/api/image-proxy?url=' + encodeURIComponent(imgUrl);
          const res = await fetch(proxied);
          if (res.ok) {
            const blob = await res.blob();
            const dataUrl = await new Promise<string>((resolve,reject)=>{const r=new FileReader();r.onload=()=>resolve(r.result as string);r.onerror=()=>reject(r.error);r.readAsDataURL(blob);});
            if (layout === 'vertical') {
              const h = pptx.height*0.4; const iw = pptx.width - margin*2; slide.addImage({data:dataUrl,x:margin,y:y,w:iw,h}); y += h + 0.2;
            } else if (layout === 'right') {
              const iw = pptx.width*0.45; const ih = pptx.height - margin*2; slide.addImage({data:dataUrl,x:pptx.width-iw-margin,y:margin,w:iw,h:ih}); w = pptx.width - iw - margin*3; x = margin;
            } else { // left/default
              const iw = pptx.width*0.45; const ih = pptx.height - margin*2; slide.addImage({data:dataUrl,x:margin,y:margin,w:iw,h:ih}); x = iw + margin*2; w = pptx.width - x - margin;
            }
          }
        } catch { /* ignore image errors */ }
      }
      if (title) { slide.addText(title, { x, y, w, fontSize: 30, bold: true }); y += 0.6; }
      if (bullets.length) { slide.addText(bullets.map(b=>`• ${b}`).join('\n'), { x, y, w, fontSize:18 }); y += Math.min(bullets.length*0.35, pptx.height - y - margin); }
      for (const p of paragraphs) {
        if (y > pptx.height - margin - 0.4) break;
        slide.addText(p, { x, y, w, fontSize:18 });
        y += 0.4;
      }
    }
    await pptx.writeFile({ fileName: `${filename}.pptx` });
  } catch (e) {
    console.warn('Structured PPTX export failed – fallback to raster.', e);
    await fallbackRasterPptx(slides, filename);
  }
}

// JSON export removed per requirement

// Helper function to get slide preview for thumbnails
export async function generateSlidePreview(
  slideIndex: number,
  width = 400,
  height = 225
): Promise<string | null> {
  const slideElement = document.querySelector(
    `.slide-wrapper-${slideIndex} .slide-container-${slideIndex}`
  );

  if (!slideElement) {
    return null;
  }

  try {
    // Wait for images to load
  await waitForImages(slideElement as HTMLElement);

    const canvas = await html2canvas(slideElement as HTMLElement, {
      scale: 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: width,
      height: height,
    });

    return canvas.toDataURL("image/png");
  } catch (error) {
    console.error("Failed to generate slide preview:", error);
    return null;
  }
}
