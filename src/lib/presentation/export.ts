import { type PlateSlide, type PlateNode, type HeadingElement, type ParagraphElement } from "@/components/presentation/utils/parser";
// pptxgenjs types (no official TS types with ESM default in some versions). Create minimal interface.
// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
// PPTX export temporarily disabled (pptxgenjs not installed in container build). Placeholder kept.
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// (downloadFile helper removed – no longer used)

// Helper function to wait for images to load
function waitForImages(element: HTMLElement): Promise<void> {
  const images = element.querySelectorAll("img");
  const promises = Array.from(images).map((img) => {
    if (img.complete) return Promise.resolve();
    return new Promise<void>((resolve) => {
      img.onload = () => resolve();
      img.onerror = () => resolve(); // Continue even if image fails to load
      // Timeout after 10 seconds
      setTimeout(() => resolve(), 10000);
    });
  });
  return Promise.all(promises).then(() => undefined);
}

// Inline external <img> elements by converting them to data URLs to stabilize exports (PDF/PPTX)
async function inlineImages(container: HTMLElement, tag: string) {
  const imgs = Array.from(container.querySelectorAll('img')).filter((el): el is HTMLImageElement => el instanceof HTMLImageElement);
  await Promise.all(imgs.map(async (img) => {
    if (!img.src || img.src.startsWith('data:')) return;
    try {
      const bust = img.src.includes('?') ? '&' : '?';
      const fetchUrl = img.src + bust + encodeURIComponent(tag) + '=1';
      let res = await fetch(fetchUrl, { mode: 'cors' });
      if (!res.ok) {
        const proxied = '/api/image-proxy?url=' + encodeURIComponent(img.src);
        res = await fetch(proxied);
        if (!res.ok) throw new Error('Proxy fetch failed: ' + res.status);
      }
      const blob = await res.blob();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(blob);
      });
      img.setAttribute('data-original-src', img.src);
      img.removeAttribute('srcset');
      img.loading = 'eager';
      img.src = dataUrl;
    } catch (err) {
      img.crossOrigin = 'anonymous';
    }
  }));
}



// Export slides as PDF
export async function exportToPDF(slides: PlateSlide[], filename: string) {
  const pdf = new jsPDF({ orientation: "landscape", unit: "px", format: [1920, 1080] });

  // Helper: convert external images to data URLs to avoid CORS / authentication issues.
  // (Removed inlineImages local function; using shared inlineImages())

  for (let i = 0; i < slides.length; i++) {
    const slideElement = document.querySelector(`.slide-wrapper-${i} .slide-container-${i}`);
    if (!slideElement) { console.warn(`Slide ${i} element not found, skipping...`); continue; }

    // Clone node to avoid mutating on-screen state while inlining
    const working = slideElement.cloneNode(true) as HTMLElement; // cloneNode returns Node
    // Fix width/height to ensure consistent rendering
    working.style.width = '1920px';
    working.style.height = '1080px';
    document.body.appendChild(working);
  await inlineImages(working, 'pdf');
    await waitForImages(working);

    // One more short delay for rendering fonts/images
    await new Promise(r => setTimeout(r, 300));

    // html2canvas on the cloned node (off-DOM)
  // already appended
    const canvas = await html2canvas(working, {
      scale: 2,
      useCORS: true,
      allowTaint: true, // allowTaint since we now mostly have data URLs
      backgroundColor: '#ffffff',
      width: 1920,
      height: 1080,
      logging: false,
      imageTimeout: 15000,
    });
  document.body.removeChild(working);

    const imgData = canvas.toDataURL('image/png');
    if (i > 0) pdf.addPage();
    pdf.addImage(imgData, 'PNG', 0, 0, 1920, 1080);
  }

  pdf.save(`${filename}.pdf`);
}

// Export slides as PNG images
// PNG export removed

// Disabled loader
// Removed PPTX text extraction while feature disabled.

// Export slides as PowerPoint (PPTX)
interface GenericNode { text?: string; children?: GenericNode[]; type?: string }

function isHeading(n: PlateNode): n is HeadingElement {
  return ["h1","h2","h3","h4","h5","h6"].includes(n.type as string);
}
function isParagraph(n: PlateNode): n is ParagraphElement { return n.type === "p"; }

function flattenText(nodes?: GenericNode[]): string {
  if (!nodes || nodes.length === 0) return "";
  const out: string[] = [];
  const stack: GenericNode[] = [...nodes];
  while (stack.length) {
    const n = stack.shift()!;
    if (typeof n.text === "string" && n.text.trim().length) out.push(n.text);
  if (n.children?.length) stack.unshift(...n.children);
  }
  return out.join(" ").replace(/\s+/g, " ").trim();
}

function extractSlideStructure(slide: PlateSlide) {
  let title: string | undefined;
  const bullets: string[] = [];
  const paragraphs: string[] = [];

  function processNode(node: PlateNode): void {
    const g = node as unknown as GenericNode;

    // Extract title from headings
    if (!title && isHeading(node)) {
      title = flattenText(g.children);
      return;
    }

    // Extract bullet points
    if (g.type === "bullets" && g.children?.length) {
      for (const child of g.children) {
        const t = flattenText(child.children);
        if (t) bullets.push(t);
      }
      return;
    }

    // Extract paragraphs
    if (isParagraph(node)) {
      const p = flattenText(g.children);
      if (p) paragraphs.push(p);
      return;
    }

    // Handle columns and other container elements
    if (g.type === "columns" && g.children?.length) {
      for (const column of g.children) {
        if (column.children?.length) {
          for (const child of column.children) {
            processNode(child as PlateNode);
          }
        }
      }
      return;
    }

    // Handle other container types that might have nested content
    if (g.children?.length && ['icons', 'cycle', 'staircase', 'visualization_list'].includes(g.type || '')) {
      for (const child of g.children) {
        const text = flattenText(child.children);
        if (text) {
          // Treat structured content as bullet points
          bullets.push(text);
        }
      }
      return;
    }

    // Fallback: extract any text content from unknown node types
    const text = flattenText(g.children);
    if (text && text.length > 0) {
      paragraphs.push(text);
    }
  }

  // Process all nodes in the slide
  for (const node of slide.content) {
    processNode(node);
  }

  return {
    title: title || "Slide",
    bullets,
    paragraphs
  };
}

async function fallbackRasterPptx(slides: PlateSlide[], filename: string) {
  const { default: PptxGen } = await import('pptxgenjs');
  const pptx = new PptxGen();
  pptx.layout = 'LAYOUT_16x9';

  for (const [i] of slides.entries()) {
    const el = document.querySelector(`.slide-wrapper-${i} .slide-container-${i}`);
    if (!el) { continue; }

    const working = el.cloneNode(true) as HTMLElement;
    working.style.width = '1920px';
    working.style.height = '1080px';
    working.style.position = 'absolute';
    working.style.left = '-9999px';
    working.style.top = '0';
    document.body.appendChild(working);

    await inlineImages(working, 'pptx');
    await waitForImages(working);
    await new Promise(r => setTimeout(r, 300));

    const canvas = await html2canvas(working, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 1920,
      height: 1080,
      logging: false,
      imageTimeout: 15000,
    });

    document.body.removeChild(working);
    const img = canvas.toDataURL('image/png');
    const slide = pptx.addSlide();

    // Use proper dimensions - pptx.width and pptx.height are in inches for 16:9 layout
    // For LAYOUT_16x9: width = 10 inches, height = 5.625 inches
    slide.addImage({
      data: img,
      x: 0,
      y: 0,
      w: '100%',
      h: '100%'
    });
  }

  await pptx.writeFile({ fileName: `${filename}.pptx` });
}

export async function exportToPPTX(slides: PlateSlide[], filename: string) {
  if (typeof window === 'undefined') return;

  try {
    const { default: PptxGen } = await import('pptxgenjs');
    const pptx = new PptxGen();
    pptx.layout = 'LAYOUT_16x9';

    for (const s of slides) {
      if (!s) continue;

      const { title, bullets, paragraphs } = extractSlideStructure(s);
      const slide = pptx.addSlide();

      // Use proper measurements in inches for 16:9 layout
      const margin = 0.5; // 0.5 inches margin
      let x = margin;
      let w = 10 - margin * 2; // Total width is ~10 inches for 16:9
      let y = margin;

      const layout = s.layoutType;
      const imgUrl = s.rootImage?.url;

      // Handle background/layout images
      if (imgUrl) {
        try {
          const proxied = '/api/image-proxy?url=' + encodeURIComponent(imgUrl);
          const res = await fetch(proxied);
          if (res.ok) {
            const blob = await res.blob();
            const dataUrl = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.onerror = () => reject(reader.error);
              reader.readAsDataURL(blob);
            });

            if (layout === 'vertical') {
              const h = 2.5; // Fixed height in inches
              const iw = 10 - margin * 2; // Full width minus margins
              slide.addImage({ data: dataUrl, x: margin, y: y, w: iw, h: h });
              y += h + 0.3;
            } else if (layout === 'right') {
              const iw = 4.5; // Image width in inches
              const ih = 5.625 - margin * 2; // Full height minus margins
              slide.addImage({
                data: dataUrl,
                x: 10 - iw - margin,
                y: margin,
                w: iw,
                h: ih
              });
              w = 10 - iw - margin * 3; // Adjust text area width
              x = margin;
            } else { // left/default
              const iw = 4.5; // Image width in inches
              const ih = 5.625 - margin * 2; // Full height minus margins
              slide.addImage({
                data: dataUrl,
                x: margin,
                y: margin,
                w: iw,
                h: ih
              });
              x = iw + margin * 2; // Adjust text start position
              w = 10 - x - margin; // Adjust text area width
            }
          }
        } catch (error) {
          console.warn('Failed to load image for slide:', error);
        }
      }

      // Add title
      if (title) {
        slide.addText(title, {
          x: x,
          y: y,
          w: w,
          h: 0.8,
          fontSize: 32,
          bold: true,
          color: '1f2937',
          fontFace: 'Arial'
        });
        y += 1.0;
      }

      // Add bullet points
      if (bullets.length) {
        const bulletText = bullets.map(b => `• ${b}`).join('\n');
        slide.addText(bulletText, {
          x: x,
          y: y,
          w: w,
          fontSize: 20,
          color: '374151',
          fontFace: 'Arial',
          lineSpacing: 24
        });
        y += Math.min(bullets.length * 0.4, 5.625 - y - margin);
      }

      // Add paragraphs
      for (const p of paragraphs) {
        if (y > 5.625 - margin - 0.5) break; // Check if we have space
        slide.addText(p, {
          x: x,
          y: y,
          w: w,
          fontSize: 18,
          color: '374151',
          fontFace: 'Arial',
          lineSpacing: 22
        });
        y += 0.5;
      }
    }

    await pptx.writeFile({ fileName: `${filename}.pptx` });

  } catch (e) {
    console.warn('Structured PPTX export failed – fallback to raster.', e);
    await fallbackRasterPptx(slides, filename);
  }
}

// JSON export removed per requirement

// Helper function to get slide preview for thumbnails
export async function generateSlidePreview(
  slideIndex: number,
  width = 400,
  height = 225
): Promise<string | null> {
  const slideElement = document.querySelector(
    `.slide-wrapper-${slideIndex} .slide-container-${slideIndex}`
  );

  if (!slideElement) {
    return null;
  }

  try {
    // Wait for images to load
  await waitForImages(slideElement as HTMLElement);

    const canvas = await html2canvas(slideElement as HTMLElement, {
      scale: 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: width,
      height: height,
    });

    return canvas.toDataURL("image/png");
  } catch (error) {
    console.error("Failed to generate slide preview:", error);
    return null;
  }
}
