// Minimal module declaration for pptxgenjs to satisfy TS when using dynamic import.
// Extended typing can be added later if needed.
declare module 'pptxgenjs' {
  interface AddImageOptions { data: string; x: number; y: number; w: number; h: number }
  interface AddTextOptions { x: number; y: number; w?: number; h?: number; fontSize?: number; bold?: boolean }
  interface Slide {
    addImage(opts: AddImageOptions): void;
    addText(text: string, opts: AddTextOptions): void;
  }
  interface WriteFileOptions { fileName: string }
  export default class PptxGenJS {
    layout: string;
    width: number;
    height: number;
    addSlide(): Slide;
    writeFile(opts: WriteFileOptions): Promise<void>;
  }
}
